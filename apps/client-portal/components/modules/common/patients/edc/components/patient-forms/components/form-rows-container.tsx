import React from "react";

import { Tooltip } from "@/components/ui/tooltip";

import type { Forms } from "../../patient-dashboard/data";
import { FormRow } from "./form-row";
import type { HiddenRowGroup, PeriodConfig } from "./types/schedule-grid-types";
import { getHiddenRowsTooltip } from "./utils/schedule-grid-utils";

// Import the existing HiddenRowDivider component
const HiddenRowDivider: React.FC<{
  keyPrefix: string;
  count: number;
  colSpan: number;
}> = ({ keyPrefix, count, colSpan }) => {
  return (
    <div
      key={keyPrefix}
      className="relative"
      style={{ gridColumn: `span ${colSpan}` }}
    >
      <Tooltip
        content={
          <span className="whitespace-nowrap">
            {getHiddenRowsTooltip(count)}
          </span>
        }
        variant="primary"
        theme={{
          target: "relative z-20 h-full w-full",
        }}
      >
        <span className="invisible">X</span>
      </Tooltip>
      <div className="absolute top-0 z-[5] h-full w-full">
        <div className="perforated-bottom h-full w-full" />
      </div>
    </div>
  );
};

type FormRowsContainerProps = {
  filteredForms: Forms[];
  periodConfig: PeriodConfig[];
  hiddenRowGroups: HiddenRowGroup[];
  totalVisibleColumns: number;
  filterStatus?: string;
};

export const FormRowsContainer: React.FC<FormRowsContainerProps> = ({
  filteredForms,
  periodConfig,
  hiddenRowGroups,
  totalVisibleColumns,
  filterStatus,
}) => {
  const generateFormRowsWithDividers = () => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const form of filteredForms) {
      // Check if we need to insert a row divider before this form
      const dividerForThisPosition = hiddenRowGroups.find(
        (group) => group.insertAfterIndex === currentVisibleIndex,
      );

      if (dividerForThisPosition) {
        elements.push(
          <HiddenRowDivider
            key={`row-divider-before-${form.id}`}
            keyPrefix={`row-divider-before-${form.id}`}
            count={dividerForThisPosition.count}
            colSpan={totalVisibleColumns + 1} // +1 for the form name column
          />,
        );
      }

      // Add the actual form row
      elements.push(
        <FormRow
          key={`form-${form.id}`}
          form={form}
          periodConfig={periodConfig}
          filterStatus={filterStatus}
        />,
      );

      currentVisibleIndex++;
    }

    // Check for row dividers at the end
    const finalRowDivider = hiddenRowGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalRowDivider) {
      elements.push(
        <HiddenRowDivider
          key="row-divider-final"
          keyPrefix="row-divider-final"
          count={finalRowDivider.count}
          colSpan={totalVisibleColumns + 1} // +1 for the form name column
        />,
      );
    }

    return elements;
  };

  return <React.Fragment>{generateFormRowsWithDividers()}</React.Fragment>;
};
