import { useMemo } from "react";

import type {
  GridLayoutData,
  HiddenColumnGroup,
  HiddenRowGroup,
  PeriodConfig,
} from "../types/schedule-grid-types";
import {
  DIVIDER_WIDTH,
  FORM_NAME_COLUMN_WIDTH,
  VISIT_COLUMN_WIDTH,
} from "../utils/schedule-grid-utils";

export const useGridLayout = (
  periodConfig: PeriodConfig[],
  filteredFormsLength: number,
  hiddenColumnGroups: HiddenColumnGroup[],
  hiddenRowGroups: HiddenRowGroup[],
): GridLayoutData => {
  const gridTemplateColumns = useMemo(() => {
    const columns: string[] = [];
    let currentVisibleIndex = 0;

    // Add the form name column
    columns.push(FORM_NAME_COLUMN_WIDTH);

    // Add visible columns and dividers (using single divider approach like rows)
    for (const config of periodConfig) {
      for (let i = 0; i < config.visits.length; i++) {
        // Check if we need to insert a divider before this column
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          columns.push(DIVIDER_WIDTH); // Single divider column per hidden group
        }

        columns.push(VISIT_COLUMN_WIDTH); // Visible visit column
        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      columns.push(DIVIDER_WIDTH);
    }

    return columns.join(" ");
  }, [periodConfig, hiddenColumnGroups]);

  const gridTemplateRows = useMemo(() => {
    const rows: string[] = [];
    let currentVisibleIndex = 0;

    // Add header rows
    rows.push("auto"); // Form Column Header
    rows.push("auto"); // Visit Windows / Days row header

    for (let i = 0; i < filteredFormsLength; i++) {
      // Check if we need to insert a row divider before this form
      const dividerForThisPosition = hiddenRowGroups.find(
        (group) => group.insertAfterIndex === currentVisibleIndex,
      );

      if (dividerForThisPosition) {
        rows.push("10px"); // Exact height for divider row
      }

      rows.push("minmax(auto, 1fr)"); // Flexible height for form row
      currentVisibleIndex++;
    }

    // Check for row dividers at the end
    const finalRowDivider = hiddenRowGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalRowDivider) {
      rows.push("10px"); // Exact height for final divider row
    }

    return rows.join(" ");
  }, [filteredFormsLength, hiddenRowGroups]);

  const totalVisibleColumns = useMemo(() => {
    return (
      periodConfig.reduce((sum, config) => sum + config.visits.length, 0) +
      hiddenColumnGroups.length
    );
  }, [periodConfig, hiddenColumnGroups]);

  const totalRows = useMemo(() => {
    // 2 header rows + filtered forms + hidden row dividers
    return 2 + filteredFormsLength + hiddenRowGroups.length;
  }, [filteredFormsLength, hiddenRowGroups]);

  return {
    gridTemplateColumns,
    gridTemplateRows,
    totalVisibleColumns,
    totalRows,
  };
};
