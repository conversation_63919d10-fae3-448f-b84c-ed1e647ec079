import React from "react";

import { Tooltip } from "@/components/ui/tooltip";

import type { HiddenColumnGroup } from "./types/schedule-grid-types";
import { getHiddenColumnsTooltip } from "./utils/schedule-grid-utils";

// Single HiddenColumnDivider component that spans all rows (like HiddenRowDivider spans all columns)
const HiddenColumnDivider: React.FC<{
  keyPrefix: string;
  count: number;
  totalRows: number;
}> = ({ keyPrefix, count, totalRows }) => {
  return (
    <div
      key={keyPrefix}
      style={{
        gridColumn: "span 1",
        gridRow: `1 / ${totalRows + 1}`, // Span from row 1 to the last row
      }}
      className="relative"
    >
      <Tooltip
        content={
          <span className="whitespace-nowrap">
            {getHiddenColumnsTooltip(count)}
          </span>
        }
        variant="primary"
        theme={{
          target: "relative z-20 h-full w-full",
        }}
      >
        <span className="invisible">X</span>
      </Tooltip>
      <div className="absolute top-0 z-[5] h-full w-full">
        <div className="perforated-right h-full w-full" />
      </div>
    </div>
  );
};

type HiddenColumnDividersProps = {
  hiddenColumnGroups: HiddenColumnGroup[];
  totalRows: number;
};

export const HiddenColumnDividers: React.FC<HiddenColumnDividersProps> = ({
  hiddenColumnGroups,
  totalRows,
}) => {
  return (
    <React.Fragment>
      {hiddenColumnGroups.map((group, index) => (
        <HiddenColumnDivider
          key={`column-divider-${group.insertAfterIndex}-${index}`}
          keyPrefix={`column-divider-${group.insertAfterIndex}-${index}`}
          count={group.count}
          totalRows={totalRows}
        />
      ))}
    </React.Fragment>
  );
};
