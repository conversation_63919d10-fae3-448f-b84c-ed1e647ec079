import { Card } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import { useEffect, useMemo, useState } from "react";

import { Button } from "@/components/ui/button";

import {
  FormPeriod,
  forms,
  formVisitProgresses,
  formVisits,
} from "../patient-dashboard/data";
import {
  ColumnVisibilityDropdown,
  FormStatusFilters,
  RowVisibilityDropdown,
  ScheduleGrid,
} from "./components";

type FilterStatus =
  | "notStarted"
  | "inProgress"
  | "activeQueries"
  | "readyForSdv"
  | "sourceDataVerified"
  | "formSigned"
  | "locked"
  | "archived";

export const PatientForm = () => {
  const [filterStatus, setFilterStatus] = useQueryState(
    "filterStatus",
    parseAsString.withDefault(""),
  );

  const [baseSelectedVisits, setBaseSelectedVisits] = useState<Set<number>>(
    () => new Set(formVisits.map((visit) => visit.id)),
  );

  const [selectedVisits, setSelectedVisits] = useState<Set<number>>(
    () => new Set(formVisits.map((visit) => visit.id)),
  );

  const [selectedForms, setSelectedForms] = useState<Set<number>>(
    () => new Set(forms.map((form) => form.id)),
  );

  useEffect(() => {
    if (!filterStatus || filterStatus === "") {
      setSelectedVisits(new Set(baseSelectedVisits));
      return;
    }

    const filteredVisits = new Set<number>();
    Array.from(baseSelectedVisits).forEach((visitId) => {
      const hasMatchingFormProgress = formVisitProgresses.some(
        (progress) =>
          progress.formVisitId === visitId &&
          progress.status === filterStatus &&
          selectedForms.has(progress.id),
      );

      if (hasMatchingFormProgress) {
        filteredVisits.add(visitId);
      }
    });

    setSelectedVisits(filteredVisits);
  }, [baseSelectedVisits, filterStatus, selectedForms]);

  useEffect(() => {
    if (!filterStatus || filterStatus === "") return;

    const formIdsWithStatus = new Set(
      formVisitProgresses
        .filter((formProgress) => formProgress.status === filterStatus)
        .map((formProgress) => formProgress.id),
    );

    setSelectedForms((prevSelected) => {
      const newSelected = new Set<number>();
      prevSelected.forEach((formId) => {
        if (formIdsWithStatus.has(formId)) {
          newSelected.add(formId);
        }
      });
      return newSelected;
    });
  }, [filterStatus]);

  const periodConfig = useMemo(() => {
    const config: Array<{ period: FormPeriod; title: string }> = [
      { period: "screening", title: "Screening" },
      { period: "treatment", title: "Treatment" },
      { period: "followUp", title: "Follow-up" },
      { period: "etd", title: "End of Treatment" },
    ];

    return config
      .map(({ period, title }) => ({
        title,
        visits: formVisits.filter((visit) => {
          const isPeriodMatch = visit.period === period;
          const isVisitSelected = selectedVisits.has(visit.id);
          return isPeriodMatch && isVisitSelected;
        }),
      }))
      .filter((config) => config.visits.length > 0); // Only include periods that have visible visits
  }, [selectedVisits]);

  const filteredForms = useMemo(() => {
    let result = forms;

    // Filter by status at the FormVisitProgress level
    if (filterStatus && filterStatus !== "") {
      // Get unique form IDs that have the selected status in any visit
      const formIdsWithStatus = new Set(
        formVisitProgresses
          .filter((formProgress) => formProgress.status === filterStatus)
          .map((formProgress) => formProgress.id),
      );

      result = result.filter((form) => formIdsWithStatus.has(form.id));
    }

    // Filter by selected forms (row visibility)
    result = result.filter((form) => selectedForms.has(form.id));

    return result;
  }, [filterStatus, selectedForms]);

  const handleFilterClick = (status: FilterStatus) => {
    // If clicking the currently selected status, reset to show all
    if (filterStatus === status) {
      setFilterStatus("");
      // Also reset selected forms when clearing filter
      setSelectedForms(new Set(forms.map((form) => form.id)));
    } else {
      // Otherwise, set the new filter
      setFilterStatus(status);
    }
  };

  const handleVisitSelection = (visitId: number, checked: boolean) => {
    setBaseSelectedVisits((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(visitId);
      } else {
        newSet.delete(visitId);
      }
      return newSet;
    });
  };

  const handleShowAllVisits = () => {
    setBaseSelectedVisits(new Set(formVisits.map((visit) => visit.id)));
  };

  const handleFormSelection = (formId: number, checked: boolean) => {
    setSelectedForms((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(formId);
      } else {
        newSet.delete(formId);
      }
      return newSet;
    });
  };

  const handleShowAllForms = () => {
    setSelectedForms(new Set(forms.map((form) => form.id)));
  };

  return (
    <Card className="h-full flex-1 rounded-xl">
      <div className="flex h-full flex-col gap-5 p-6">
        <div className="h-full rounded-lg">
          {/* Header and filters */}
          <div className="mb-5 flex items-center justify-between pt-4">
            <h2 className="text-xl font-semibold leading-5">
              Schedule of Activities
            </h2>

            <FormStatusFilters
              filterStatus={filterStatus}
              onFilterClick={handleFilterClick}
            />
          </div>

          <div className="mb-5 flex justify-between">
            <div className="flex gap-4">
              <RowVisibilityDropdown
                selectedForms={selectedForms}
                onFormSelection={handleFormSelection}
                onShowAllForms={handleShowAllForms}
              />
              <ColumnVisibilityDropdown
                selectedVisits={selectedVisits}
                baseSelectedVisits={baseSelectedVisits}
                onVisitSelection={handleVisitSelection}
                onShowAllVisits={handleShowAllVisits}
              />
            </div>

            <Button
              variant="outline"
              onClick={() => {
                handleShowAllVisits();
                handleShowAllForms();
                setFilterStatus("");
              }}
            >
              Reset View
            </Button>
          </div>

          <ScheduleGrid
            periodConfig={periodConfig}
            filteredForms={filteredForms}
            selectedVisits={selectedVisits}
            selectedForms={selectedForms}
            filterStatus={filterStatus}
          />
        </div>
      </div>
    </Card>
  );
};
