import React from "react";

import { FormRowsContainer } from "./form-rows-container";
import { GridHeader } from "./grid-header";
import { useGridLayout } from "./hooks/use-grid-layout";
import { useHiddenGroups } from "./hooks/use-hidden-groups";
import type { ScheduleGridProps } from "./types/schedule-grid-types";

export const ScheduleGrid: React.FC<ScheduleGridProps> = ({
  periodConfig,
  filteredForms,
  selectedVisits,
  selectedForms,
  filterStatus,
}) => {
  const { hiddenColumnGroups, hiddenRowGroups } = useHiddenGroups(
    selectedVisits,
    selectedForms,
  );

  const {
    gridTemplateColumns,
    gridTemplateRows,
    totalVisibleColumns,
    totalVisibleRows,
  } = useGridLayout(
    periodConfig,
    filteredForms.length,
    hiddenColumnGroups,
    hiddenRowGroups,
  );

  return (
    <div
      className="grid flex-1 overflow-x-auto rounded-lg border"
      style={{
        gridTemplateColumns,
        gridTemplateRows,
      }}
    >
      <GridHeader
        periodConfig={periodConfig}
        hiddenColumnGroups={hiddenColumnGroups}
        totalVisibleRows={totalVisibleRows}
      />

      <FormRowsContainer
        filteredForms={filteredForms}
        periodConfig={periodConfig}
        hiddenRowGroups={hiddenRowGroups}
        totalVisibleColumns={totalVisibleColumns}
        filterStatus={filterStatus}
      />
    </div>
  );
};
