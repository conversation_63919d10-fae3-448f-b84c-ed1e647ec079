import React from "react";

import { Tooltip } from "@/components/ui/tooltip";

import type {
  HiddenColumnGroup,
  PeriodConfig,
} from "./types/schedule-grid-types";
import { getHiddenColumnsTooltip } from "./utils/schedule-grid-utils";

// HiddenColumnDivider component - each row renders its own divider
const HiddenColumnDivider: React.FC<{ keyPrefix: string; count: number }> = ({
  keyPrefix,
  count,
}) => {
  return (
    <div key={keyPrefix} style={{ gridColumn: "span 1" }} className="relative">
      <Tooltip
        content={
          <span className="whitespace-nowrap">
            {getHiddenColumnsTooltip(count)}
          </span>
        }
        variant="primary"
        theme={{
          target: "relative z-20 h-full w-full",
        }}
      >
        <span className="invisible">X</span>
      </Tooltip>
      <div className="absolute top-0 z-[5] h-full w-full">
        <div className="perforated-right h-full w-full" />
      </div>
    </div>
  );
};

type GridHeaderProps = {
  periodConfig: PeriodConfig[];
  hiddenColumnGroups: HiddenColumnGroup[];
};

export const GridHeader: React.FC<GridHeaderProps> = ({
  periodConfig,
  hiddenColumnGroups,
}) => {
  const generatePeriodHeaders = () => {
    const headerElements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const config of periodConfig) {
      // Check if we need a divider before this period
      const dividerBeforePeriod = hiddenColumnGroups.find(
        (group) => group.insertAfterIndex === currentVisibleIndex,
      );

      if (dividerBeforePeriod) {
        headerElements.push(
          <HiddenColumnDivider
            key={`period-divider-before-${config.title}`}
            keyPrefix={`period-divider-before-${config.title}`}
            count={dividerBeforePeriod.count}
          />,
        );
      }

      // Calculate how many columns this period should span (including any dividers within)
      let periodSpan = config.visits.length;
      const periodStartIndex = currentVisibleIndex;
      const periodEndIndex = currentVisibleIndex + config.visits.length;

      // Count dividers that fall within this period
      const dividersInPeriod = hiddenColumnGroups.filter(
        (group) =>
          group.insertAfterIndex > periodStartIndex &&
          group.insertAfterIndex < periodEndIndex,
      ).length;

      periodSpan += dividersInPeriod;

      headerElements.push(
        <div
          key={config.title}
          className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]"
          style={{ gridColumn: `span ${periodSpan}` }}
        >
          {config.title}
        </div>,
      );

      currentVisibleIndex += config.visits.length;
    }

    // Check for final divider
    const finalPeriodDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalPeriodDivider) {
      headerElements.push(
        <HiddenColumnDivider
          key="period-divider-final"
          keyPrefix="period-divider-final"
          count={finalPeriodDivider.count}
        />,
      );
    }

    return headerElements;
  };

  const generateVisitHeaders = () => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const config of periodConfig) {
      for (let i = 0; i < config.visits.length; i++) {
        const visit = config.visits[i];

        // Check if we need to insert a divider before this visit
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          elements.push(
            <HiddenColumnDivider
              key={`divider-before-${visit.id}`}
              keyPrefix={`divider-before-${visit.id}`}
              count={dividerForThisPosition.count}
            />,
          );
        }

        elements.push(
          <div
            key={`header-${visit.id}`}
            className="flex items-center whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]"
          >
            {visit.name} / Day {visit.visitDay}{" "}
            {visit.visitWindow > 0 ? `± ${visit.visitWindow}d` : ""}
          </div>,
        );

        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      elements.push(
        <HiddenColumnDivider
          key="divider-final"
          keyPrefix="divider-final"
          count={finalDivider.count}
        />,
      );
    }

    return elements;
  };

  return (
    <React.Fragment>
      {/* Form Column Header */}
      <div className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]">
        Visit / Activity
      </div>

      {/* Period Column Headers with dividers */}
      {generatePeriodHeaders()}

      {/* Visit Windows / Days row header */}
      <div className="whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]">
        Visit Windows / Days
      </div>

      {/* Visit headers with dividers */}
      {generateVisitHeaders()}
    </React.Fragment>
  );
};
