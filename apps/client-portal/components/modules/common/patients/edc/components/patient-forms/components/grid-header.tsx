import React from "react";

import type { PeriodConfig } from "./types/schedule-grid-types";

type GridHeaderProps = {
  periodConfig: PeriodConfig[];
};

export const GridHeader: React.FC<GridHeaderProps> = ({ periodConfig }) => {
  const generatePeriodHeaders = () => {
    const headerElements: React.ReactElement[] = [];

    for (const config of periodConfig) {
      // Period header spans all visits in this period (no divider logic needed)
      headerElements.push(
        <div
          key={config.title}
          className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]"
          style={{ gridColumn: `span ${config.visits.length}` }}
        >
          {config.title}
        </div>,
      );
    }

    return headerElements;
  };

  const generateVisitHeaders = () => {
    const elements: React.ReactElement[] = [];

    for (const config of periodConfig) {
      for (let i = 0; i < config.visits.length; i++) {
        const visit = config.visits[i];

        // Only render visit headers, dividers are handled by HiddenColumnDividers
        elements.push(
          <div
            key={`header-${visit.id}`}
            className="flex items-center whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]"
          >
            {visit.name} / Day {visit.visitDay}{" "}
            {visit.visitWindow > 0 ? `± ${visit.visitWindow}d` : ""}
          </div>,
        );
      }
    }

    return elements;
  };

  return (
    <React.Fragment>
      {/* Form Column Header */}
      <div className="whitespace-nowrap bg-gray-200 px-2.5 py-2 text-center text-xs font-bold leading-[18px]">
        Visit / Activity
      </div>

      {/* Period Column Headers with dividers */}
      {generatePeriodHeaders()}

      {/* Visit Windows / Days row header */}
      <div className="whitespace-nowrap border border-gray-100 px-2.5 py-2 text-center text-[10px] font-semibold leading-[18px]">
        Visit Windows / Days
      </div>

      {/* Visit headers with dividers */}
      {generateVisitHeaders()}
    </React.Fragment>
  );
};
