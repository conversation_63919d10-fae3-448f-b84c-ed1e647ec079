import { FormPeriod, Forms } from "../../../patient-dashboard/data";

export type PeriodConfig = {
  title: string;
  visits: Array<{
    id: number;
    name: string;
    subName: string;
    visitDay: number;
    visitWindow: number;
    period: FormPeriod;
    forms: Forms[];
  }>;
};

export type ScheduleGridProps = {
  periodConfig: PeriodConfig[];
  filteredForms: Forms[];
  selectedVisits: Set<number>;
  selectedForms: Set<number>;
  filterStatus?: string;
};

export type HiddenColumnGroup = {
  startIndex: number;
  endIndex: number;
  insertAfterIndex: number;
  count: number;
};

export type HiddenRowGroup = {
  startIndex: number;
  endIndex: number;
  insertAfterIndex: number;
  count: number;
};

export type GridLayoutData = {
  gridTemplateColumns: string;
  gridTemplateRows: string;
  totalVisibleColumns: number;
  totalRows: number;
};

export type HiddenGroupsData = {
  hiddenColumnGroups: HiddenColumnGroup[];
  hiddenRowGroups: HiddenRowGroup[];
};
