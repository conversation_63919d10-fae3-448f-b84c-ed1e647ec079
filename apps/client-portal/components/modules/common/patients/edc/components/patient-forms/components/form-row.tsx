import React from "react";

import type { Forms } from "../../patient-dashboard/data";
import { FormCell } from "./form-cell";
import type { PeriodConfig } from "./types/schedule-grid-types";
// HiddenColumnDivider is now handled in GridHeader and spans all rows

type FormRowProps = {
  form: Forms;
  periodConfig: PeriodConfig[];
  filterStatus?: string;
};

export const FormRow: React.FC<FormRowProps> = ({
  form,
  periodConfig,
  filterStatus,
}) => {
  const generateFormCells = () => {
    const elements: React.ReactElement[] = [];

    for (const config of periodConfig) {
      for (const visit of config.visits) {
        // Only render form cells, dividers are handled in GridHeader and span all rows
        elements.push(
          <FormCell
            key={`${form.id}-${visit.id}`}
            form={form}
            visit={visit}
            config={config}
            filterStatus={filterStatus}
          />,
        );
      }
    }

    return elements;
  };

  return (
    <React.Fragment>
      {/* Form name column */}
      <div className="whitespace-nowrap border border-gray-100 px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500">
        {form.name}
      </div>

      {/* Form cells with dividers */}
      {generateFormCells()}
    </React.Fragment>
  );
};
