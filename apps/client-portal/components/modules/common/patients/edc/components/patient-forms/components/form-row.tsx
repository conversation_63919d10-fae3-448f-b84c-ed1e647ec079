import React from "react";

import { Tooltip } from "@/components/ui/tooltip";

import type { Forms } from "../../patient-dashboard/data";
import { FormCell } from "./form-cell";
import type {
  HiddenColumnGroup,
  PeriodConfig,
} from "./types/schedule-grid-types";
import { getHiddenColumnsTooltip } from "./utils/schedule-grid-utils";

// HiddenColumnDivider component
const HiddenColumnDivider: React.FC<{ keyPrefix: string; count: number }> = ({
  keyPrefix,
  count,
}) => {
  return (
    <div key={keyPrefix} style={{ gridColumn: "span 1" }} className="relative">
      <Tooltip
        content={
          <span className="whitespace-nowrap">
            {getHiddenColumnsTooltip(count)}
          </span>
        }
        variant="primary"
        theme={{
          target: "relative z-20 h-full w-full",
        }}
      >
        <span className="invisible">X</span>
      </Tooltip>
      <div className="absolute top-0 z-[5] h-full w-full">
        <div className="perforated-right h-full w-full" />
      </div>
    </div>
  );
};

type FormRowProps = {
  form: Forms;
  periodConfig: PeriodConfig[];
  hiddenColumnGroups: HiddenColumnGroup[];
  filterStatus?: string;
};

export const FormRow: React.FC<FormRowProps> = ({
  form,
  periodConfig,
  hiddenColumnGroups,
  filterStatus,
}) => {
  const generateFormCells = () => {
    const elements: React.ReactElement[] = [];
    let currentVisibleIndex = 0;

    for (const config of periodConfig) {
      for (const visit of config.visits) {
        // Check if we need to insert a divider before this visit
        const dividerForThisPosition = hiddenColumnGroups.find(
          (group) => group.insertAfterIndex === currentVisibleIndex,
        );

        if (dividerForThisPosition) {
          elements.push(
            <HiddenColumnDivider
              key={`divider-${form.id}-before-${visit.id}`}
              keyPrefix={`divider-${form.id}-before-${visit.id}`}
              count={dividerForThisPosition.count}
            />,
          );
        }

        elements.push(
          <FormCell
            key={`${form.id}-${visit.id}`}
            form={form}
            visit={visit}
            config={config}
            filterStatus={filterStatus}
          />,
        );

        currentVisibleIndex++;
      }
    }

    // Check for dividers at the end
    const finalDivider = hiddenColumnGroups.find(
      (group) => group.insertAfterIndex === currentVisibleIndex,
    );
    if (finalDivider) {
      elements.push(
        <HiddenColumnDivider
          key={`divider-${form.id}-final`}
          keyPrefix={`divider-${form.id}-final`}
          count={finalDivider.count}
        />,
      );
    }

    return elements;
  };

  return (
    <React.Fragment>
      {/* Form name column */}
      <div className="whitespace-nowrap border border-gray-100 px-4 py-3 text-center text-[10px] font-medium leading-[18px] text-gray-500">
        {form.name}
      </div>

      {/* Form cells with dividers */}
      {generateFormCells()}
    </React.Fragment>
  );
};
